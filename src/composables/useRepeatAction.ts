import type { Callback } from '../types/shared/utils'

interface RepeatActionReturnType {
  start: Callback
  stop: Callback
}

interface UseRepeatActionOptions {
  action: Callback
  finalInterval?: number
  initialInterval?: number
  accelerationDuration?: number
}

export function useRepeatAction(action: Callback): RepeatActionReturnType
export function useRepeatAction(options: UseRepeatActionOptions): RepeatActionReturnType
export function useRepeatAction(actionOrOptions: UseRepeatActionOptions | Callback): RepeatActionReturnType {
  const {
    action,
    finalInterval = 1000 / 10,
    initialInterval = 500,
    accelerationDuration = 2000,
  } = typeof actionOrOptions === 'function' ? { action: actionOrOptions } : actionOrOptions

  let timer: ReturnType<typeof setTimeout> | null = null
  let startTime = 0

  const step = () => {
    action()

    const elapsedTime = Date.now() - startTime
    let nextInterval: number

    if (elapsedTime >= accelerationDuration) {
      nextInterval = finalInterval
    } else {
      const progress = elapsedTime / accelerationDuration
      nextInterval = initialInterval - (initialInterval - finalInterval) * progress
    }

    timer = setTimeout(step, nextInterval)
  }

  const start = () => {
    if (timer) {
      return
    }

    startTime = Date.now()
    action()

    timer = setTimeout(step, initialInterval)
  }

  const stop = () => {
    if (timer) {
      clearTimeout(timer)
      timer = null
    }
  }

  return {
    start,
    stop,
  }
}
