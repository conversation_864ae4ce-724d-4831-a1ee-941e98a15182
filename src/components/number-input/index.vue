<script lang="ts" setup>
import type { InputProps } from '../../types/components/input'
import { useModelValue } from '../../composables/useModelValue'
import PlusIcon from '@gdsicon/vue/plus'
import MinusIcon from '@gdsicon/vue/minus'
import PInput from '../input/index.vue'
import { useRepeatAction } from '../../composables/useRepeatAction'

interface Props {
  min?: number
  max?: number
  step?: number
  scientific?: boolean
  modelValue?: number | null
}

defineOptions({
  name: 'PNumberInput',
  inheritAttrs: false,
  model: {
    prop: 'modelValue',
    event: 'update:modelValue',
  },
})

const props = withDefaults(
  defineProps<Props>(),
  {
    step: 1,
    min: Number.MIN_SAFE_INTEGER,
    max: Number.MAX_SAFE_INTEGER,
    scientific: true,
  },
)

const emits = defineEmits<{
  'update:modelValue': [number]
}>()

const {
  start: startIncrease,
  stop: stopIncrease,
} = useRepeatAction(increaseValue)

const {
  start: startDecrease,
  stop: stopDecrease,
} = useRepeatAction(decreaseValue)

const computedModelValue = useModelValue(props, emits, {
  // get: (): number => {
  //   return formatModelValue(props.modelValue)!
  // },
})

function formatModelValue<T>(value: T): number {
  const digitizeValue = Number(value)

  if (Number.isNaN(digitizeValue)) {
    return 0
  }

  return digitizeValue
}

function onInputChange(ev: InputProps['modelValue']) {
  const digitizeValue = formatModelValue(ev)
}

function increaseValue() {
  computedModelValue.value += props.step
}

function decreaseValue() {
  computedModelValue.value -= props.step
}

const INTEGER_REGEX = /^-?\d+$/
const INTEGER_REGEX_WITH_SCIENTIFIC = /^-?\d+\.?\d*(e-?\d+)?$/

function onInputKeydown(ev: KeyboardEvent) {
  if (ev.key === 'ArrowUp') {
    ev.preventDefault()
    increaseValue()
    console.info('🌿index.vue:69/(props.step):\n', props.step)
  } else if (ev.key === 'ArrowDown') {
    ev.preventDefault()
    decreaseValue()
  }
}
</script>

<template>
  {{ computedModelValue }} - {{ typeof computedModelValue }}
  <PInput
    inputmode="decimal"
    v-bind="$attrs"
    :min="min"
    :max="max"
    align="center"
    :prefix-style="false"
    :suffix-style="false"
    :model-value="computedModelValue"
    @keydown="onInputKeydown"
  >
    <template #prefix>
      <button
        class="appearance-none outline-none px-3 h-full text-foreground-secondary hover:bg-gray-alpha-100 hover:text-gray-1000 active:bg-gray-alpha-300 motion-safe:transition-colors"
        @click="decreaseValue"
        @pointerdown="startDecrease"
        @pointercancel="stopDecrease"
        @pointerup="stopDecrease"
      >
        <MinusIcon class="pointer-events-none" />
      </button>
    </template>
    <template #suffix>
      <button
        class="appearance-none outline-none px-3 h-full text-foreground-secondary hover:bg-gray-alpha-100 hover:text-gray-1000 active:bg-gray-alpha-300 motion-safe:transition-colors"
        @click="increaseValue"
        @pointerdown="startIncrease"
        @pointercancel="stopIncrease"
        @pointerup="stopIncrease"
      >
        <PlusIcon class="pointer-events-none" />
      </button>
    </template>
  </PInput>
</template>
