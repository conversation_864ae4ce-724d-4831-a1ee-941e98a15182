<script lang="ts" setup>
import type { InputProps } from '../../types/components/input'
import CrossIcon from '@gdsicon/vue/cross'
import EyeIcon from '@gdsicon/vue/eye'
import EyeOffIcon from '@gdsicon/vue/eye-off'
import { computed, shallowRef } from 'vue'
import { useConfigProvider } from '../../composables/useConfigProviderContext'
import { useModelValue } from '../../composables/useModelValue'
import { isTruthyProp } from '../../utils/format'
import { getUniqueId } from '../../utils/uid'
import { getFallbackValue } from '../../utils/value'
import PError from '../error/index.vue'

defineOptions({
  name: 'PInput',
  model: {
    prop: 'modelValue',
    event: 'update:modelValue',
  },
})

const props = withDefaults(
  defineProps<InputProps>(),
  {
    align: 'left',
    min: Number.MIN_SAFE_INTEGER,
    max: Number.MAX_SAFE_INTEGER,
    modelValue: '',
    prefixStyle: true,
    suffixStyle: true,
  },
)

const emits = defineEmits<{
  'update:modelValue': [InputProps['modelValue']]
  'focus': [FocusEvent]
  'blur': [FocusEvent]
  'change': [InputProps['modelValue']]
  'keydown': [KeyboardEvent]
}>()

const SIZES = {
  xs: 'h-6 text-xs',
  sm: 'h-7.5 text-sm',
  md: 'h-9 text-sm',
  lg: 'h-10 text-base',
}

const ALIGN = {
  left: 'text-left',
  center: 'text-center',
  right: 'text-right',
}

const uniqueId = getUniqueId()
const inputRef = shallowRef<HTMLInputElement>()

const config = useConfigProvider()
const modelValue = useModelValue(props, emits)

const isPasswordVisible = shallowRef(!props.password)
const internalInputType = computed(() => props.inputType || isPasswordVisible.value ? 'text' : 'password')

const computedClass = computed(() => {
  const classes = ['pxd-input--border relative flex items-center overflow-hidden rounded-md bg-background-100 motion-safe:transition-all']

  classes.push(getFallbackValue(props.size, SIZES, config.size))

  if (isTruthyProp(props.disabled)) {
    classes.push('is-disabled')
  }

  if (isTruthyProp(props.readonly)) {
    classes.push('is-readonly')
  }

  if (props.error) {
    classes.push('is-error')
  }

  return classes.join(' ')
})

function onInputFocus(event: FocusEvent) {
  emits('focus', event)
}

function onInputBlur(event: FocusEvent) {
  emits('blur', event)
}

function onInputChange(event: Event) {
  emits('change', (event.target as HTMLInputElement).value)
}

function onInputKeydown(event: KeyboardEvent) {
  emits('keydown', event)
}

function togglePasswordType() {
  isPasswordVisible.value = !isPasswordVisible.value
}

function clearInputValue() {
  modelValue.value = ''
}

const blur = () => inputRef.value?.blur()
const focus = () => inputRef.value?.focus()
const select = () => inputRef.value?.select()

defineExpose({
  blur,
  focus,
  select,
  clear: clearInputValue,
})
</script>

<template>
  <label class="pxd-input block w-full max-w-full" :for="uniqueId" @dragstart.prevent>
    <div v-if="label || $slots.label" class="pxd-form--label">
      <slot name="label">{{ label }}</slot>
    </div>

    <div :class="computedClass">
      <div
        v-if="$slots.prefix"
        class="pxd-input--prefix text-sm flex h-full items-center text-gray-700"
        :class="{ 'px-3 rounded-l-inherit border-r bg-background-200': prefixStyle }"
      >
        <slot name="prefix" />
      </div>

      <input
        :id="uniqueId"
        ref="inputRef"
        v-model="modelValue"
        class="px-3 size-full rounded-inherit bg-transparent outline-none file:font-medium file:border-0 file:bg-transparent placeholder:text-gray-600 placeholder:select-none disabled:cursor-not-allowed disabled:bg-gray-100 disabled:text-gray-700 disabled:placeholder:text-gray-400"
        :class="{ 'pr-9': password || allowClear, [ALIGN[align]]: true }"
        :type="internalInputType"
        :min="min"
        :max="max"
        autocorrect="off"
        autocomplete="off"
        autocapitalize="off"
        :readonly="readonly"
        :disabled="disabled"
        :required="required"
        :inputmode="inputmode"
        :minlength="minlength"
        :maxlength="maxlength"
        :autofocus="autofocus"
        :placeholder="placeholder"
        @blur="onInputBlur"
        @focus="onInputFocus"
        @change="onInputChange"
        @keydown="onInputKeydown"
      >

      <div
        v-if="password || allowClear"
        v-show="modelValue"
        class="pxd-input--icon right-0 top-0 absolute flex h-full cursor-pointer items-center rounded-r-inherit text-foreground-secondary hover:bg-gray-alpha-100 hover:text-gray-1000 active:bg-gray-alpha-300 motion-safe:transition-colors"
      >
        <div v-if="password" class="p-3" @click.prevent="togglePasswordType">
          <EyeOffIcon v-if="isPasswordVisible" class="size-3" />
          <EyeIcon v-else class="size-3" />
        </div>
        <div v-if="allowClear" class="p-3" @click.prevent="clearInputValue">
          <CrossIcon class="size-3" />
        </div>
      </div>

      <div
        v-if="$slots.suffix"
        class="pxd-input--suffix text-sm flex h-full items-center text-gray-700"
        :class="{ 'px-3 rounded-r-inherit border-l bg-background-200': suffixStyle }"
      >
        <slot name="suffix" />
      </div>
    </div>

    <PError v-if="error" class="mt-2" :size="size">
      {{ error }}
    </PError>
  </label>
</template>
